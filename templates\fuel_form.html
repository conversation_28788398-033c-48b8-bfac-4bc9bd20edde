{% extends 'base.html' %}

{% block content %}
<div class="max-w-5xl mx-auto bg-white p-6 rounded-lg shadow-md">
    <header class="text-center mb-6">
        <h4 class="text-sm font-semibold my-1">Republic of the Philippines</h4>
        <h4 class="text-sm font-semibold my-1">PROVINCE OF ZAMBOANGA DEL SUR</h4>
        <h4 class="text-sm font-semibold my-1">MUNICIPALITY OF DUMINGAG</h4>
        <h4 class="text-sm font-semibold my-1">OFFICE OF THE {{ office }}</h4>
    </header>

    <form method="post" class="space-y-6">
        {% csrf_token %}
        
        {% if form.errors %}
        <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
            Please correct the errors below.
        </div>
        {% endif %}

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label class="block text-sm font-medium text-gray-700">Vehicle</label>
                {{ form.vehicle }}
                {% if form.vehicle.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.vehicle.errors.0 }}</p>
                {% endif %}
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700">Driver</label>
                {{ form.driver }}
                {% if form.driver.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.driver.errors.0 }}</p>
                {% endif %}
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700">Destination</label>
                {{ form.destination }}
                {% if form.destination.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.destination.errors.0 }}</p>
                {% endif %}
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700">Date</label>
                {{ form.date }}
                {% if form.date.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.date.errors.0 }}</p>
                {% endif %}
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700">Trip Number</label>
                {{ form.trip_number }}
                {% if form.trip_number.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.trip_number.errors.0 }}</p>
                {% endif %}
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700">Number of Trips</label>
                {{ form.number_of_trips }}
                {% if form.number_of_trips.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.number_of_trips.errors.0 }}</p>
                {% endif %}
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700">Purpose</label>
                {{ form.purpose }}
                {% if form.purpose.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.purpose.errors.0 }}</p>
                {% endif %}
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700">Fuel Amount (Liters)</label>
                {{ form.total_liters }}
                {% if form.total_liters.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.total_liters.errors.0 }}</p>
                {% endif %}
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700">Cost (₱)</label>
                {{ form.cost }}
                {% if form.cost.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.cost.errors.0 }}</p>
                {% endif %}
            </div>
        </div>

        <div class="flex justify-end space-x-3 mt-6">
            <a href="{% url 'dashboard' %}" 
               class="bg-gray-200 px-4 py-2 rounded hover:bg-gray-300">
                Cancel
            </a>
            <button type="submit" 
                    class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                Save Entry
            </button>
        </div>
    </form>
</div>
{% endblock %}
