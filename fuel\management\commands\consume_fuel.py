import random
from datetime import date, timedelta
from django.core.management.base import BaseCommand
from fuel.models import Driver, FuelConsumption

class Command(BaseCommand):
    help = 'Simulate fuel consumption with separate gas slips for multiple trips per day using a fixed price of ₱56.50 per liter'

    def handle(self, *args, **kwargs):
        # Set up driver shifts.
        driver_shifts = {
            "Ambulance L300": [
                ("Antonio <PERSON>ebro", "Ambulance L300"),
                ("Grace Zaldy Matos", "Ambulance L300"),
                ("Rey Berjame", "Ambulance L300")
            ],
            "Ambulance Province": [
                ("Humphrey Daryl Ginggo", "Ambulance Province"),
                ("Jeweriel Sulatorio", "Ambulance Province")
            ],
            "Ambulance DOH": [
                ("Crisbanie Jay Paran", "Ambulance DOH"),
                ("Aldren <PERSON>rot", "Ambulance DOH")
            ],
            "Backhoe": [
                ("<PERSON>", "Backhoe"),
                ("<PERSON>", "Backhoe")  # Same driver for both shifts
            ],
            "Dumptruck": [
                ("Raymond Hangcan", "Dumptruck"),
                ("Raymond Hangcan", "Dumptruck")  # Same driver for both shifts
            ]
        }

        drivers_by_vehicle = {}
        for vehicle, driver_list in driver_shifts.items():
            drivers_by_vehicle[vehicle] = []
            for name, vehicle_type in driver_list:
                driver, created = Driver.objects.get_or_create(
                    name=name,
                    defaults={'vehicle': vehicle_type}
                )
                if not created and driver.vehicle != vehicle_type:
                    driver.vehicle = vehicle_type
                    driver.save()
                drivers_by_vehicle[vehicle].append(driver)

        # Fixed price per liter.
        PRICE_PER_LITER = 56.50

        # Allocation constants
        TOTAL_BUDGET = 847500.0  # Increased budget to accommodate heavy equipment
        TOTAL_LITERS = TOTAL_BUDGET / PRICE_PER_LITER

        # Destination-based fuel budget (in pesos)
        destinations = [
            ('dipolog', 2500),
            ('cagayan', 5000),
            ('margosatubig', 2000),
            ('pagadian_city', 1000),
            ('ozamiz_city', 1500),
            ('zamboanga_city', 5000),
            ('ipil', 3500),
            ('sindangan', 1500),
            ('local', 2000),
        ]

        # Create a dictionary for easy lookup
        destinations_dict = dict(destinations)

        # Simulation period: October 8, 2024 to December 31, 2024.
        start_date = date(2024, 10, 8)
        end_date = date(2024, 12, 31)
        num_days = (end_date - start_date).days + 1

        remaining_fuel = TOTAL_LITERS

        # Dictionary to track trip_number for each (driver.id, date)
        trip_numbers = {}

        for n in range(num_days):
            current_date = start_date + timedelta(n)
            # Optionally skip a specific date if needed (e.g., December 27, 2024)
            if current_date == date(2024, 12, 27):
                continue

            shift_day = n % 6  # For driver shift rotation (3 drivers, 2 days each)

            for vehicle, driver_list in drivers_by_vehicle.items():
                # Handle different numbers of drivers per vehicle
                if len(driver_list) == 3:  # Ambulance L300 has 3 drivers
                    if shift_day < 2:
                        active_driver = driver_list[0]  # Antonio Tenebro
                    elif shift_day < 4:
                        active_driver = driver_list[1]  # Grace Zaldy Matos
                    else:
                        active_driver = driver_list[2]  # Rey Berjame
                elif len(driver_list) == 2:  # Other vehicles have 2 drivers
                    active_driver = driver_list[0] if shift_day < 3 else driver_list[1]
                else:  # Single driver vehicles
                    active_driver = driver_list[0]
                # Two trips on Sundays; one trip on other days.
                trips_today = random.randint(2, 3) if current_date.weekday() == 6 else random.randint(1, 2)

                for _ in range(trips_today):
                    if remaining_fuel <= 0:
                        break

                    # Select purpose and destination based on vehicle type
                    if active_driver.vehicle in ["Backhoe", "Dumptruck"]:
                        # Infrastructure/emergency purposes for heavy equipment
                        infrastructure_purposes = [
                            'reroute river manlabay',
                            'landslide dapiwak',
                            'landslide san vicente',
                            'landslide senote'
                        ]
                        selected_purpose = random.choice(infrastructure_purposes)
                        selected_destination = 'local'  # Infrastructure work is typically local
                        required_liters = 400  # Fixed 400 liters for heavy equipment
                        trip_cost = required_liters * PRICE_PER_LITER
                    else:
                        # Transport patient for ambulances
                        selected_purpose = "Transport Patient"
                        selected_destination, budget_amount = random.choice(destinations)
                        required_liters = budget_amount / PRICE_PER_LITER
                        trip_cost = budget_amount

                    if remaining_fuel >= required_liters:
                        trip_liters = required_liters
                    else:
                        trip_liters = remaining_fuel
                        trip_cost = trip_liters * PRICE_PER_LITER

                    # Determine unique trip number using our in‐memory dictionary.
                    key = (active_driver.id, current_date)
                    trip_numbers[key] = trip_numbers.get(key, 0) + 1
                    trip_number = trip_numbers[key]
                    FuelConsumption.objects.create(
                          driver=active_driver,
                          date=current_date,
                          trip_number=trip_number,
                          number_of_trips=1,
                          purpose=selected_purpose,
                          destination=selected_destination,
                          total_liters=trip_liters,
                          cost=trip_cost,
                          vehicle=active_driver.vehicle
                    )
                    remaining_fuel -= trip_liters
                if remaining_fuel <= 0:
                    break
            if remaining_fuel <= 0:
                break

        self.stdout.write(
            self.style.SUCCESS(
                f'Fuel consumption simulation completed for October 8, 2024 to December 31, 2024.\n'
                f'Remaining fuel: {remaining_fuel:.2f}L\n'
                f'Remaining budget: ₱{remaining_fuel * PRICE_PER_LITER:.2f}'
            )
        )