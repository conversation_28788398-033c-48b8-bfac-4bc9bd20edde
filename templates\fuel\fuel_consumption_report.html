{% extends 'base.html' %}

{% block extra_head %}
<style>
    @page {
        size: landscape;
        margin: 1cm;
    }
    
    @media print {
        body {
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
            background-color: #FFFFFF;
            font-family: Arial, sans-serif;
        }
        
        .no-print {
            display: none;
        }
        
        .report-container {
            border: 3px solid #000;
            padding: 1cm;
            margin: 0;
        }
        
        .report-header {
            border-bottom: 2px solid #000;
            padding-bottom: 0.8cm;
            margin-bottom: 0.8cm;
            background: linear-gradient(to bottom, #f8f9fa 0%, #ffffff 100%);
        }
        
        .report-title {
            font-size: 24pt;
            font-weight: bold;
            margin: 0.8cm 0;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        th {
            background-color: #f0f0f0 !important;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 11pt;
        }
        
        td {
            padding: 12px 8px !important;
        }
        
        .report-footer {
            margin-top: 1.5cm;
        }
    }
    
    /* Styles for both screen and print */
    .report-container {
        background-color: white;
        padding: 3rem;
        border-radius: 0.75rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        border: 3px solid #000;
    }
    
    .report-table {
        width: 100%;
        border-collapse: collapse;
        border: 3px solid #000;
    }
    
    .report-table th {
        background-color: #f3f4f6;
        font-weight: 600;
        border: 1.5px solid #000;
        padding: 12px 10px;
        text-align: center;
        text-transform: uppercase;
        font-size: 0.9rem;
        letter-spacing: 0.5px;
    }
    
    .report-table td {
        border: 1.5px solid #000;
        padding: 10px 8px;
    }
    
    .report-table tr:hover {
        background-color: #f8f9fa;
    }
    
    .report-table tr:nth-child(even) {
        background-color: #f9fafb;
    }
    
    .header-title {
        font-size: 1.1rem;
        letter-spacing: 0.5px;
        text-transform: uppercase;
    }
    
    .report-title {
        font-size: 2rem;
        letter-spacing: 1px;
        margin: 1.5rem 0;
        text-transform: uppercase;
        color: #1a1a1a;
    }
    
    .signature-section {
        margin-top: 2rem;
        padding-top: 1rem;
    }
    
    .signature-line {
        border-top: 1.5px solid #000;
        width: 80%;
        margin: 1.5rem auto 0.3rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="max-w-full mx-auto p-6">
    <!-- Filter Controls (not printed) -->
    <div class="no-print mb-4 bg-gray-100 p-4 rounded">
        <form method="get" class="flex flex-wrap gap-4 items-end">
            <div>
                <label class="block text-sm font-medium text-gray-700">Start Date</label>
                <input type="date" name="start_date" value="{{ request.GET.start_date }}" 
                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">End Date</label>
                <input type="date" name="end_date" value="{{ request.GET.end_date }}"
                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
            </div>
            <div>
                <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                    Filter Report
                </button>
            </div>
            <div>
                <button type="button" onclick="window.print()" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                    Print Report
                </button>
            </div>
        </form>
    </div>

    <div class="report-container">
        <div class="report-header text-center mb-8">
            <h4 class="header-title font-semibold my-2">Republic of the Philippines</h4>
            <h4 class="header-title font-semibold my-2">PROVINCE OF ZAMBOANGA DEL SUR</h4>
            <h4 class="header-title font-semibold my-2">MUNICIPALITY OF DUMINGAG</h4>
            <h4 class="header-title font-semibold my-2">OFFICE OF THE {{ office }}</h4>
            <h2 class="report-title font-bold">FUEL CONSUMPTION REPORT</h2>
            <p class="text-sm mt-3">Report Period: 
                {% if request.GET.start_date and request.GET.end_date %}
                    {{ request.GET.start_date|date:"F d, Y" }} to {{ request.GET.end_date|date:"F d, Y" }}
                {% else %}
                    All Time
                {% endif %}
            </p>
            <p class="text-sm">Generated on: {{ report_date|date:"F d, Y" }}</p>
        </div>

        <!-- Table -->
        <table class="report-table">
            <thead>
                <tr>
                    <th class="w-16">No.</th>
                    <th class="w-32">Date</th>
                    <th class="w-32">Diesel (L)</th>
                    <th class="w-32">Gasoline (L)</th>
                    <th class="w-48">Driver's Name</th>
                    <th>Remarks</th>
                </tr>
            </thead>
            <tbody>
                {% for entry in entries %}
                <tr>
                    <td class="text-center">{{ forloop.counter }}</td>
                    <td class="text-center">{{ entry.date|date:"M d, Y" }}</td>
                    <td class="text-center">
                        {% if entry.vehicle == "Ambulance L300" or entry.vehicle == "Ambulance Province" or entry.vehicle == "Ambulance DOH" %}
                            {{ entry.total_liters|floatformat:2 }}
                        {% else %}
                            -
                        {% endif %}
                    </td>
                    <td class="text-center">
                        {% if entry.vehicle == "Ambulance L300" or entry.vehicle == "Ambulance Province" or entry.vehicle == "Ambulance DOH" %}
                            -
                        {% else %}
                            {{ entry.total_liters|floatformat:2 }}
                        {% endif %}
                    </td>
                    <td>{{ entry.driver.name }}</td>
                    <td>{{ entry.purpose }}</td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="6" class="text-center py-4">No fuel consumption records found.</td>
                </tr>
                {% endfor %}
            </tbody>
            <tfoot>
                <tr>
                    <td colspan="2" class="text-right font-bold">Total:</td>
                    <td class="text-center font-bold">{{ diesel_total|floatformat:2 }}</td>
                    <td class="text-center font-bold">{{ gasoline_total|floatformat:2 }}</td>
                    <td colspan="2" class="text-right font-bold">Grand Total: {{ grand_total|floatformat:2 }} L</td>
                </tr>
            </tfoot>
        </table>

        <!-- Footer -->
        <div class="report-footer mt-8 grid grid-cols-2 gap-8">
            <div>
                <p class="font-semibold">Prepared by:</p>
                <div class="mt-8">
                    <div class="signature-line"></div>
                    <p class="font-semibold">_______________________</p>
                    <p>Administrative Officer</p>
                </div>
            </div>
            <div>
                <p class="font-semibold">Noted by:</p>
                <div class="mt-8">
                    <div class="signature-line"></div>
                    <p class="font-semibold">_______________________</p>
                    <p>Municipal Mayor</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
