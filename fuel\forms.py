from django import forms
from .models import FuelConsumption, Driver

class FuelConsumptionForm(forms.ModelForm):
    VEHICLE_CHOICES = [
        ('Ambulance L300', 'Ambulance L300'),
        ('Ambulance Province', 'Ambulance Province'),
        ('Ambulance DOH', 'Ambulance DOH'),
        ('Backhoe', 'Backhoe'),
        ('Du<PERSON>ruck', 'Dumptruck'),
    ]

    vehicle = forms.ChoiceField(choices=VEHICLE_CHOICES)
    driver = forms.ModelChoiceField(
        queryset=Driver.objects.all(),
        empty_label="Select Driver"
    )
    
    class Meta:
        model = FuelConsumption
        fields = [
            'driver',
            'date',
            'vehicle',
            'destination',
            'trip_number',
            'number_of_trips',
            'purpose',
            'total_liters',
            'cost'
        ]
        widgets = {
            'date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'form-control'
            }),
            'trip_number': forms.NumberInput(attrs={
                'min': 1,
                'class': 'form-control'
            }),
            'number_of_trips': forms.NumberInput(attrs={
                'min': 1,
                'class': 'form-control'
            }),
            'total_liters': forms.NumberInput(attrs={
                'step': '0.01',
                'class': 'form-control'
            }),
            'cost': forms.NumberInput(attrs={
                'step': '0.01',
                'class': 'form-control'
            })
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['purpose'].initial = "Transport Patient"
        self.fields['cost'].label = "Cost (₱)"
        self.fields['total_liters'].label = "Fuel Amount (Liters)"
