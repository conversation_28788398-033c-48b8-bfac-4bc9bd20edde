{% extends "base.html" %}
{% load humanize %}
{% block top_nav %}
{% endblock %}
{% block content %}
<script src="https://cdn.tailwindcss.com"></script>
<div class="min-h-screen bg-white py-8">
    <div class="grid grid-cols-2 gap-4 max-w-[210mm] mx-auto px-4">
        {% for slip in slips %}
        <div class="bg-white rounded-sm border-2 border-gray-200 h-[125mm] relative">
            <div class="border-b-2 border-blue-900 py-2 px-3">
                <div class="flex justify-between items-center">
                    <div class="text-left">
                        <h1 class="text-xl font-bold text-blue-900">GAS SLIP</h1>
                        <p class="text-xs text-gray-600">MDRRMO-DUMINGAG</p>
                    </div>
                    <div class="text-right">
                        <p class="text-xs text-gray-500">Ref: #{{ slip.id|stringformat:"04d" }}</p>
                    </div>
                </div>
            </div>

            <div class="px-3 py-3">
                <div class="grid grid-cols-2 gap-x-4 gap-y-2 text-xs">
                    <div class="col-span-2">
                        <h3 class="font-semibold text-blue-900 mb-2">TRANSACTION DETAILS</h3>
                        <table class="w-full">
                            <tbody>
                                <tr>
                                    <td class="w-1/3 py-1 text-gray-600">Date:</td>
                                    <td class="py-1 font-medium">{{ slip.date|date:"d M Y" }}</td>
                                </tr>
                                <tr>
                                    <td class="py-1 text-gray-600">Driver:</td>
                                    <td class="py-1 font-medium">{{ slip.driver.name }}</td>
                                </tr>
                                <tr>
                                    <td class="py-1 text-gray-600">Vehicle:</td>
                                    <td class="py-1 font-medium">{{ slip.vehicle }}</td>
                                </tr>
                                <tr>
                                    <td class="py-1 text-gray-600">Purpose:</td>
                                    <td class="py-1 font-medium">{{ slip.purpose }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                      <div class="col-span-2 border-t border-gray-200 pt-4">
                          <h3 class="font-semibold text-blue-900 mb-2">FUEL INFORMATION</h3>
                          <div class="grid grid-cols-3 gap-4">
                              <div>
                                  <p class="text-gray-600">Price per Liter</p>
                                  <p class="text-lg font-bold text-blue-900">₱{{ slip.FUEL_PRICE|floatformat:2 }}</p>
                              </div>
                              <div>
                                  <p class="text-gray-600">Liters Dispensed</p>
                                  <p class="text-lg font-bold text-blue-900">{{ slip.total_liters|floatformat:2 }} L</p>
                              </div>
                              <div>
                                  <p class="text-gray-600">Total Cost</p>
                                  <p class="text-lg font-bold text-blue-900">₱{{ slip.cost|floatformat:2 }}</p>
                              </div>
                          </div>
                      </div>
                  </div>
            </div>
            <div class="absolute bottom-0 w-full px-4 pb-4">
                <div class="grid grid-cols-2 gap-4 text-xs">
                    <div class="border-t-2 border-gray-300 pt-2">
                        <p class="font-semibold text-gray-700">Authorized By:</p>
                    </div>
                    <div class="border-t-2 border-gray-300 pt-2">
                        <p class="font-semibold text-gray-700">Received By:</p>
                        
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <div class="mt-8 text-center print:hidden">
        <button onclick="window.print()" class="bg-blue-900 text-white px-8 py-3 rounded-sm hover:bg-blue-800 transition-colors mr-4">
            Print Slips
        </button>
        <a href="{% url 'dashboard' %}" class="bg-gray-100 text-gray-700 px-8 py-3 rounded-sm hover:bg-gray-200 transition-colors">
            Return to Dashboard
        </a>
    </div>
</div>

<style>
    @media print {
        @page {
            size: A4;
            margin: 5mm;
        }
        
        body {
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
            background: white;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 5mm !important;
        }
        
        .border-blue-900 {
            border-color: #1e3a8a !important;
        }
        
        .bg-white {
            height: 125mm !important;
            margin-bottom: 5mm !important;
            page-break-inside: avoid;
        }
        
        .grid > div:nth-child(4n) {
            page-break-after: always;
        }
    }
</style>
{% endblock %}