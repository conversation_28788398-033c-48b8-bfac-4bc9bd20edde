{% extends "base.html" %}

{% block content %}
<div class="max-w-2xl mx-auto bg-white p-6 rounded-lg shadow">
    <h2 class="text-2xl font-bold mb-6">Add New Driver</h2>
    
    <form method="post">
        {% csrf_token %}
        <div class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    Driver Name
                </label>
                <input type="text" name="name" 
                       class="w-full px-3 py-2 border rounded-lg"
                       required
                       placeholder="Enter driver's full name">
                {% if form.errors.name %}
                <p class="text-red-500 text-sm mt-1">{{ form.errors.name.0 }}</p>
                {% endif %}
            </div>
        </div>
        
        <div class="mt-6 flex justify-end space-x-3">
            <a href="{% url 'driver_list' %}" 
               class="bg-gray-200 px-4 py-2 rounded hover:bg-gray-300">Cancel</a>
            <button type="submit" 
                    class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                Add Driver
            </button>
        </div>
    </form>
</div>
{% endblock %}